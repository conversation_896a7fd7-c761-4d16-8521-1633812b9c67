const set = require('regenerate')(0xAD0);
set.addRange(0x951, 0x952).addRange(0x964, 0x965).addRange(0xA81, 0xA83).addRange(0xA85, 0xA8D).addRange(0xA8F, 0xA91).addRange(0xA93, 0xAA8).addRange(0xAAA, 0xAB0).addRange(0xAB2, 0xAB3).addRange(0xAB5, 0xAB9).addRange(0xABC, 0xAC5).addRange(0xAC7, 0xAC9).addRange(0xACB, 0xACD).addRange(0xAE0, 0xAE3).addRange(0xAE6, 0xAF1).addRange(0xAF9, 0xAFF).addRange(0xA830, 0xA839);
exports.characters = set;
