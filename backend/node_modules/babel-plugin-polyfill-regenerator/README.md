# babel-plugin-polyfill-regenerator

## Install

Using npm:

```sh
npm install --save-dev babel-plugin-polyfill-regenerator
```

or using yarn:

```sh
yarn add babel-plugin-polyfill-regenerator --dev
```

## Usage

Add this plugin to your Babel configuration:

```json
{
  "plugins": [["polyfill-regenerator", { "method": "usage-global" }]]
}
```

This package supports the `usage-pure`, `usage-global`, and `entry-global` methods.
When `entry-global` is used, it replaces imports to `regenerator-runtime`.
