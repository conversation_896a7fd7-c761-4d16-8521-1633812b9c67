/* TreeView.css - Refactorizado para usar variables CSS del tema Maltego */

.tree-view-container {
  width: 100%;
  height: 100vh;
  background: var(--maltego-bg);
  position: relative;
  overflow: hidden;
  font-family: var(--maltego-font-family);
}

/* Nuevo estilo para TreeView simplificado */
.tree-view-simple {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
  position: relative;
  overflow: hidden;
}

.tree-svg {
  width: 100%;
  height: 100%;
  cursor: grab;
  background: transparent;
}

.tree-svg:active {
  cursor: grabbing;
}

/* Estilos para nodos centrales */
.central-node {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.central-node:hover {
  transform: scale(1.08);
}

.central-node circle {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  animation: pulse-glow 3s ease-in-out infinite;
}

.central-node:hover circle {
  filter: drop-shadow(0 0 40px rgba(0, 212, 255, 1)) !important;
  animation: pulse-glow-fast 1s ease-in-out infinite;
}

/* Animaciones */
@keyframes pulse-glow {
  0%, 100% {
    filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.6));
  }
  50% {
    filter: drop-shadow(0 0 30px rgba(0, 212, 255, 0.8));
  }
}

@keyframes pulse-glow-fast {
  0%, 100% {
    filter: drop-shadow(0 0 30px rgba(0, 212, 255, 0.8));
  }
  50% {
    filter: drop-shadow(0 0 50px rgba(0, 212, 255, 1));
  }
}

/* Estilos para nodos de categorías */
.category-node {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  animation: fadeInScale 0.6s ease-out forwards;
}

.category-node:hover {
  transform: scale(1.12);
}

.category-node circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.category-node:hover circle {
  filter: drop-shadow(0 0 25px rgba(76, 175, 80, 0.8)) !important;
}

@keyframes fadeInScale {
  0% {
    opacity: 0;
    transform: scale(0.5);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Estilos para nodos de herramientas */
.tool-node {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  animation: fadeInScale 0.8s ease-out forwards;
}

.tool-node:hover {
  transform: scale(1.2);
}

.tool-node circle {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tool-node:hover circle {
  filter: drop-shadow(0 0 20px rgba(255, 152, 0, 0.8)) !important;
}

/* Enlaces */
.category-link, .tool-link {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 0;
  animation: fadeInLine 0.8s ease-out forwards;
}

.category-link:hover, .tool-link:hover {
  stroke-width: 4 !important;
  stroke-opacity: 1 !important;
}

@keyframes fadeInLine {
  0% {
    opacity: 0;
    stroke-dasharray: 1000;
    stroke-dashoffset: 1000;
  }
  100% {
    opacity: 0.6;
    stroke-dasharray: none;
    stroke-dashoffset: 0;
  }
}

/* Efectos adicionales */
.tree-container {
  animation: fadeIn 1s ease-out;
}

@keyframes fadeIn {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}

/* Controles del árbol */
.tree-controls {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  z-index: 10;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(10px);
  padding: 12px 20px;
  border-radius: 12px;
  border: 1px solid rgba(0, 212, 255, 0.3);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
}

.reset-button {
  background: linear-gradient(135deg, #00D4FF 0%, #0099CC 100%);
  border: none;
  color: white;
  padding: 8px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 10px rgba(0, 212, 255, 0.3);
}

.reset-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 212, 255, 0.5);
  background: linear-gradient(135deg, #00E5FF 0%, #00B8D4 100%);
}

.reset-button:active {
  transform: translateY(0);
}

.tree-info {
  color: #00D4FF;
  font-size: 14px;
  font-weight: 500;
}

.tree-info span {
  margin-right: 8px;
}

/* Responsive Design */
@media (max-width: 768px) {
  .tree-controls {
    flex-direction: column;
    gap: 10px;
    padding: 10px 15px;
  }

  .tree-info {
    font-size: 12px;
    text-align: center;
  }

  .reset-button {
    font-size: 12px;
    padding: 6px 12px;
  }

  .central-node text {
    font-size: 14px !important;
  }

  .category-node text {
    font-size: 10px !important;
  }

  .tool-node text {
    font-size: 8px !important;
  }
}

@media (max-width: 480px) {
  .tree-controls {
    left: 10px;
    right: 10px;
    top: 10px;
  }

  .central-node circle {
    r: 50 !important;
  }

  .category-node circle {
    r: 30 !important;
  }

  .tool-node circle {
    r: 15 !important;
  }
}

/* === HEADER SECTION === */
.tree-view-header {
  position: absolute;
  top: var(--maltego-spacing-lg);
  left: var(--maltego-spacing-lg);
  right: var(--maltego-spacing-lg);
  z-index: 10;
  background: rgba(0, 0, 0, 0.9);
  backdrop-filter: blur(15px);
  padding: var(--maltego-spacing-xl);
  border-radius: var(--maltego-border-radius-xl);
  box-shadow: var(--maltego-shadow-xl), var(--maltego-glow-primary);
  border: 2px solid var(--maltego-border-active);
}

.header-content {
  display: flex;
  flex-direction: column;
  gap: var(--maltego-spacing-sm);
}

.tree-view-header h2 {
  margin: 0;
  color: var(--maltego-primary);
  font-size: 1.8rem;
  font-weight: 700;
  text-shadow: var(--maltego-glow-primary);
}

.status-info {
  display: flex;
  gap: var(--maltego-spacing-lg);
  font-size: 0.9rem;
  color: var(--maltego-text-secondary);
}

.status-info span {
  padding: var(--maltego-spacing-xs) var(--maltego-spacing-sm);
  background: var(--maltego-primary-alpha);
  border-radius: 20px;
  border: 1px solid rgba(0, 212, 255, 0.3);
}

/* === CONTROLS === */
.tree-controls {
  display: flex;
  gap: var(--maltego-spacing-md);
  margin-top: var(--maltego-spacing-md);
  flex-wrap: wrap;
}

.control-btn {
  padding: var(--maltego-spacing-sm) var(--maltego-spacing-lg);
  background: var(--maltego-gradient-button);
  color: var(--maltego-text-primary);
  border: none;
  border-radius: var(--maltego-border-radius-lg);
  cursor: pointer;
  font-weight: 700;
  font-size: 0.9rem;
  transition: var(--maltego-transition-medium);
  box-shadow: var(--maltego-shadow-md);
  position: relative;
  overflow: hidden;
}

.control-btn:before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  transition: left 0.5s;
}

.control-btn:hover:before {
  left: 100%;
}

.control-btn:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--maltego-shadow-lg), var(--maltego-glow-primary);
  background: var(--maltego-gradient-button-hover);
}

.control-btn:active {
  transform: translateY(-1px) scale(1.02);
  box-shadow: var(--maltego-shadow-md), var(--maltego-glow-primary);
}

/* === GRAPH CONTAINER === */
.graph-container {
  flex: 1;
  position: relative;
  overflow: hidden;
  background: radial-gradient(circle at center, rgba(0, 20, 40, 0.8) 0%, rgba(0, 0, 0, 0.9) 100%);
}

.maltego-graph {
  width: 100%;
  height: 100vh;
  background: var(--maltego-bg);
  position: relative;
  overflow: hidden;
}

.maltego-graph svg {
  width: 100%;
  height: 100%;
  cursor: grab;
  filter: drop-shadow(0 0 20px rgba(0, 212, 255, 0.1));
}

.maltego-graph svg:active {
  cursor: grabbing;
}

.graph-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  background: 
    radial-gradient(circle at 20% 20%, var(--maltego-primary-alpha) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(138, 43, 226, 0.05) 0%, transparent 50%);
}

.graph-svg {
  width: 100%;
  height: 100%;
  cursor: grab;
  background: transparent;
}

.graph-svg:active {
  cursor: grabbing;
}

/* === NODOS BASE === */
.node {
  cursor: pointer;
  transition: var(--maltego-transition-medium);
}

.node circle {
  stroke-width: 3;
  transition: var(--maltego-transition-medium);
  filter: var(--maltego-shadow-md);
}

.node:hover circle {
  stroke-width: 4;
  filter: var(--maltego-shadow-lg);
}

.node text {
  font-family: var(--maltego-font-family);
  font-weight: 600;
  text-anchor: middle;
  dominant-baseline: central;
  pointer-events: none;
  text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.8);
  transition: var(--maltego-transition-fast);
}

/* === NODO CENTRAL === */
.node.central circle {
  fill: url(#centralGradient);
  stroke: var(--maltego-node-central);
  stroke-width: 4;
  filter: drop-shadow(0 0 20px var(--maltego-node-central)) drop-shadow(0 0 40px var(--maltego-node-central-glow));
}

.node.central text {
  fill: var(--maltego-text-primary);
  font-size: 18px;
  font-weight: 800;
  text-shadow: var(--maltego-glow-primary);
}

/* === NODOS DE CATEGORÍA === */
.node.category circle {
  fill: url(#categoryGradient);
  stroke: var(--maltego-node-category);
  stroke-width: 3;
  filter: drop-shadow(0 0 15px var(--maltego-node-category)) drop-shadow(0 0 30px var(--maltego-node-category-glow));
}

.node.category.expanded circle {
  fill: url(#expandedCategoryGradient);
  stroke: var(--maltego-node-category-expanded);
  stroke-width: 4;
  filter: drop-shadow(0 0 20px var(--maltego-accent-purple)) drop-shadow(0 0 40px rgba(138, 43, 226, 0.6));
}

.node.category text {
  fill: var(--maltego-text-primary);
  font-size: 14px;
  font-weight: 700;
}

/* === NODOS DE HERRAMIENTAS === */
.node.tool circle {
  fill: url(#toolGradient);
  stroke: var(--maltego-node-tool);
  stroke-width: 2;
  filter: drop-shadow(0 0 10px var(--maltego-accent-orange)) drop-shadow(0 0 20px rgba(255, 107, 53, 0.3));
}

.node.tool.verified circle {
  fill: url(#verifiedToolGradient);
  stroke: var(--maltego-node-tool-verified);
  filter: drop-shadow(0 0 12px var(--maltego-accent-green)) drop-shadow(0 0 24px var(--maltego-glow-verified));
}

.node.tool text {
  fill: var(--maltego-text-primary);
  font-size: 11px;
  font-weight: 600;
}

/* === ENLACES === */
.link {
  stroke-width: 2;
  stroke-opacity: 0.8;
  transition: var(--maltego-transition-medium);
  filter: var(--maltego-shadow-sm);
}

.link.category-link {
  stroke: var(--maltego-link-category);
  stroke-width: 3;
  filter: drop-shadow(0 0 5px var(--maltego-primary-alpha));
}

.link.tool-link {
  stroke: var(--maltego-link-tool);
  stroke-width: 2;
  stroke-dasharray: 5,5;
  filter: drop-shadow(0 0 3px rgba(255, 107, 53, 0.4));
}

.link:hover {
  stroke-opacity: 1;
  stroke-width: 4;
  stroke: var(--maltego-link-hover);
  filter: drop-shadow(0 0 8px rgba(255, 255, 255, 0.6));
}

/* === ANIMACIONES === */
@keyframes pulse {
  0% {
    transform: scale(1);
    filter: drop-shadow(0 0 10px var(--maltego-node-central-glow));
  }
  50% {
    transform: scale(1.08);
    filter: drop-shadow(0 0 20px var(--maltego-node-central-glow));
  }
  100% {
    transform: scale(1);
    filter: drop-shadow(0 0 10px var(--maltego-node-central-glow));
  }
}

@keyframes glow {
  0%, 100% {
    filter: drop-shadow(0 0 5px var(--maltego-primary-alpha));
  }
  50% {
    filter: drop-shadow(0 0 15px var(--maltego-glow-primary-strong));
  }
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes expandNode {
  from {
    transform: scale(0.5);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

/* === ESTADOS DE ANIMACIÓN === */
.node.active {
  animation: pulse 2s infinite ease-in-out;
}

.node.central {
  animation: glow 3s infinite ease-in-out;
}

.node.expanding {
  animation: expandNode 0.4s var(--maltego-transition-bounce);
}

.node.new {
  animation: fadeIn 0.6s var(--maltego-transition-medium);
}

/* === EFECTOS HOVER MEJORADOS === */
.graph-container .node {
  transition: var(--maltego-transition-medium);
}

.graph-container .central-node circle {
  filter: drop-shadow(0 0 20px var(--maltego-node-central-glow));
  animation: pulse 2s infinite;
}

.graph-container .category-node circle {
  filter: drop-shadow(0 0 15px var(--maltego-node-category-glow));
  transition: var(--maltego-transition-medium);
}

.graph-container .category-node:hover circle {
  filter: drop-shadow(0 0 25px var(--maltego-glow-primary-strong));
  transform: scale(1.1);
}

.graph-container .tool-node circle {
  filter: drop-shadow(0 0 10px var(--maltego-node-tool-glow));
  transition: var(--maltego-transition-medium);
}

.graph-container .tool-node:hover circle {
  filter: drop-shadow(0 0 20px var(--maltego-glow-tool));
  transform: scale(1.2);
}

/* === ICONOGRAFÍA MEJORADA === */
.node-icon {
  font-family: 'Segoe UI Emoji', 'Apple Color Emoji', 'Segoe UI Symbol';
  font-size: 1.2em;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.5));
}

.node-label {
  font-family: var(--maltego-font-family);
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
  font-weight: 600;
}

.tools-count {
  font-family: var(--maltego-font-family);
  opacity: 0.9;
  font-size: 0.8em;
  fill: var(--maltego-text-secondary);
}

/* === INFORMACIÓN DE CATEGORÍA SELECCIONADA === */
.selected-category-info {
  position: absolute;
  top: 120px;
  right: var(--maltego-spacing-lg);
  width: 320px;
  background: rgba(0, 0, 0, 0.95);
  backdrop-filter: blur(20px);
  border: 2px solid var(--maltego-border-active);
  border-radius: var(--maltego-border-radius-xl);
  padding: var(--maltego-spacing-xl);
  color: var(--maltego-text-primary);
  box-shadow: var(--maltego-shadow-xl), var(--maltego-glow-primary);
  animation: slideInRight 0.4s var(--maltego-transition-medium);
  z-index: 100;
}

.selected-category-info .close-btn {
  position: absolute;
  top: var(--maltego-spacing-md);
  right: var(--maltego-spacing-md);
  background: none;
  border: none;
  color: var(--maltego-text-secondary);
  font-size: 1.5rem;
  cursor: pointer;
  transition: var(--maltego-transition-fast);
  padding: var(--maltego-spacing-xs);
  border-radius: var(--maltego-border-radius-sm);
}

.selected-category-info .close-btn:hover {
  color: var(--maltego-primary);
  background: var(--maltego-primary-alpha);
  transform: scale(1.1);
}

.selected-category-info h3 {
  margin: 0 0 var(--maltego-spacing-md) 0;
  color: var(--maltego-primary);
  font-size: 1.4rem;
  font-weight: 700;
}

.selected-category-info .category-description {
  color: var(--maltego-text-secondary);
  margin-bottom: var(--maltego-spacing-lg);
  line-height: 1.5;
}

.selected-category-info .tools-list {
  max-height: 300px;
  overflow-y: auto;
}

.selected-category-info .tool-item {
  padding: var(--maltego-spacing-sm);
  margin-bottom: var(--maltego-spacing-xs);
  background: var(--maltego-primary-alpha);
  border-radius: var(--maltego-border-radius-md);
  border-left: 3px solid var(--maltego-primary);
  transition: var(--maltego-transition-fast);
}

.selected-category-info .tool-item:hover {
  background: rgba(0, 212, 255, 0.2);
  transform: translateX(5px);
}

.selected-category-info .tool-name {
  font-weight: 600;
  color: var(--maltego-text-primary);
  margin-bottom: var(--maltego-spacing-xs);
}

.selected-category-info .tool-description {
  font-size: 0.9rem;
  color: var(--maltego-text-muted);
  line-height: 1.4;
}

/* === TOOLTIPS === */
.node-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.95);
  color: var(--maltego-text-primary);
  padding: var(--maltego-spacing-sm) var(--maltego-spacing-md);
  border-radius: var(--maltego-border-radius-md);
  font-size: 0.9rem;
  pointer-events: none;
  z-index: 1000;
  border: 1px solid var(--maltego-border-active);
  box-shadow: var(--maltego-shadow-lg);
  backdrop-filter: blur(10px);
  animation: fadeIn 0.2s ease-out;
}

/* === RESPONSIVE === */
@media (max-width: 768px) {
  .tree-view-header {
    left: var(--maltego-spacing-sm);
    right: var(--maltego-spacing-sm);
    padding: var(--maltego-spacing-md);
  }
  
  .selected-category-info {
    right: var(--maltego-spacing-sm);
    width: calc(100% - 2rem);
  }
  
  .tree-controls {
    flex-direction: column;
  }
  
  .control-btn {
    width: 100%;
    justify-content: center;
  }
}

/* === ACCESIBILIDAD === */
.node:focus {
  outline: 2px solid var(--maltego-primary);
  outline-offset: 2px;
}

.control-btn:focus {
  outline: 2px solid var(--maltego-primary);
  outline-offset: 2px;
}

@media (prefers-reduced-motion: reduce) {
  .node.central,
  .node.active {
    animation: none;
  }
  
  .node,
  .link,
  .control-btn {
    transition: none;
  }
}

/* === ESTADOS DE BÚSQUEDA === */
.node.search-highlight circle {
  stroke: var(--maltego-accent-orange);
  stroke-width: 4;
  filter: drop-shadow(0 0 15px var(--maltego-accent-orange)) drop-shadow(0 0 30px rgba(255, 152, 0, 0.6));
  animation: pulse 1.5s infinite ease-in-out;
}

.node.search-dimmed {
  opacity: 0.3;
}

.link.search-dimmed {
  opacity: 0.2;
}